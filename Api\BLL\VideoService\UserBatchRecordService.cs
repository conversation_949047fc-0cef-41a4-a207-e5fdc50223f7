using BLL.Common;
using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using Common.JWT;
using DAL.VideoDAL;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Entity.Entitys.SysEntity;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace BLL.VideoService
{
    /// <summary>
    /// 用户批次记录业务服务类
    /// 整合观看、答题、红包功能的统一服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserBatchRecordService(
        UserBatchRecordDAL userBatchRecordDAL,
        UserDAL userDAL,
        BatchDAL batchDAL,
        VideoDAL videoDAL,
        SysUserDAL sysUserDAL,
        SysLogService logService,
        UserAuditService userAuditService,
        ILogger<UserBatchRecordService> logger) : BasePermissionService(userDAL, sysUserDAL)
    {
        private readonly UserBatchRecordDAL _userBatchRecordDAL = userBatchRecordDAL;
        private readonly BatchDAL _batchDAL = batchDAL;
        private readonly VideoDAL _videoDAL = videoDAL;
        private readonly SysLogService _logService = logService;
        private readonly UserAuditService _userAuditService = userAuditService;
        private readonly ILogger<UserBatchRecordService> _logger = logger;

        #region 记录创建和获取

        /// <summary>
        /// 创建或获取用户批次记录
        /// 用户进入视频页面时调用，确保有记录存在
        /// </summary>
        /// <param name="createDto">创建记录DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>用户批次记录</returns>
        public async Task<UserBatchRecordResponseDto> CreateOrGetRecordAsync(UserBatchRecordCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 如果UserId为空，自动创建IP用户
            string userId = createDto.UserId;
            if (string.IsNullOrEmpty(userId))
            {
                // 为匿名用户创建IP用户
                var ipUser = await CreateIPUserIfNeededAsync("127.0.0.1");
                if (ipUser == null)
                {
                    throw new BusinessException("无法为匿名用户创建IP用户");
                }
                userId = ipUser.Id;
            }

            // 验证用户是否存在，如果不存在则尝试创建IP用户
            var user = await _userDAL.GetByIdAsync(userId);
            if (user == null)
            {
                // 尝试创建IP用户（基于用户ID判断是否为IP用户）
                user = await CreateIPUserIfNeededAsync(userId);
                if (user == null)
                {
                    throw new BusinessException("指定的用户不存在");
                }
            }

            // 验证批次是否存在且有效
            var batch = await _batchDAL.GetByIdAsync(createDto.BatchId) ?? throw new BusinessException("指定的批次不存在");

            // 临时注释掉批次状态检查，用于测试匿名访问功能
            // if (batch.Status != 1)
            //     throw new BusinessException("批次未启用，无法观看");

            if (DateTime.Now < batch.StartTime)
                throw new BusinessException("批次尚未开始");

            if (DateTime.Now > batch.EndTime)
                throw new BusinessException("批次已结束");

            // 验证视频是否存在
            _ = await _videoDAL.GetByIdAsync(batch.VideoId) ?? throw new BusinessException("批次关联的视频不存在");

            // 检查用户审核状态
            var canWatch = await _userAuditService.CanUserWatchVideoAsync(userId);
            if (!canWatch)
                throw new BusinessException("用户审核未通过，无法观看视频");

            // 创建或获取记录
            var record = await _userBatchRecordDAL.CreateOrGetAsync(userId, createDto.BatchId, currentUserInfo, createDto.PromotionLink);

            // 记录日志
            _logService.Log("UserBatchRecord", $"用户 {user.Nickname} 访问批次 {batch.Name}",
                userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

            // 转换为响应DTO
            return await ConvertToResponseDtoAsync(record);
        }

        /// <summary>
        /// 获取用户批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>用户批次记录</returns>
        public async Task<UserBatchRecordResponseDto?> GetRecordAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);
            return record == null ? null : await ConvertToResponseDtoAsync(record);
        }

        /// <summary>
        /// 获取用户的所有批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户批次记录列表</returns>
        public async Task<List<UserBatchRecordSummaryDto>> GetUserRecordsAsync(string userId)
        {
            var records = await _userBatchRecordDAL.GetUserRecordsAsync(userId);
            var result = new List<UserBatchRecordSummaryDto>();

            foreach (var record in records)
            {
                var batch = await _batchDAL.GetByIdAsync(record.BatchId);
                result.Add(new UserBatchRecordSummaryDto
                {
                    Id = record.Id,
                    UserId = record.UserId,
                    BatchId = record.BatchId,
                    BatchName = batch?.Name,
                    VideoCoverUrl = batch?.VideoCoverUrl,
                    WatchProgressPercent = record.WatchProgressPercent,
                    IsCompleted = record.IsCompleted,
                    HasAnswered = record.HasAnswered,
                    CorrectRate = record.CorrectRate,
                    RewardAmount = record.RewardAmount,
                    RewardStatus = record.RewardStatus,
                    CreateTime = record.CreateTime
                });
            }

            return result;
        }

        #endregion

        #region 观看进度管理

        /// <summary>
        /// 更新观看进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="updateDto">观看进度更新DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateWatchProgressAsync(string userId, WatchProgressUpdateDto updateDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证记录是否存在
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, updateDto.BatchId);
            if (record == null)
                throw new BusinessException("用户批次记录不存在");

            // 更新观看进度
            var success = await _userBatchRecordDAL.UpdateWatchProgressAsync(
                userId,
                updateDto.BatchId,
                updateDto.ViewDuration,
                updateDto.WatchProgress,
                updateDto.IsCompleted,
                currentUserInfo);

            if (success && updateDto.IsCompleted)
            {
                // 完播时记录日志
                var user = await _userDAL.GetByIdAsync((string)userId);
                var batch = await _batchDAL.GetByIdAsync(updateDto.BatchId);
                _logService.Log("UserBatchRecord", $"用户 {user?.Nickname} 完播视频 {batch?.Name}",
                    userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

                _logger.LogInformation("用户 {UserId} 完播批次 {BatchId}", userId, updateDto.BatchId);
            }

            return success;
        }

        /// <summary>
        /// 检查用户是否已观看
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已观看</returns>
        public async Task<bool> HasWatchedAsync(string userId, int batchId)
        {
            return await _userBatchRecordDAL.HasWatchedAsync(userId, batchId);
        }

        /// <summary>
        /// 检查用户是否完播
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否完播</returns>
        public async Task<bool> HasCompletedAsync(string userId, int batchId)
        {
            return await _userBatchRecordDAL.HasCompletedAsync(userId, batchId);
        }

        /// <summary>
        /// 获取用户观看状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>观看状态信息</returns>
        public async Task<WatchStatusDto> GetWatchStatusAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);

            if (record == null)
            {
                return new WatchStatusDto
                {
                    HasRecord = false,
                    HasWatched = false,
                    IsCompleted = false,
                    WatchProgress = 0,
                    ViewDuration = 0
                };
            }

            return new WatchStatusDto
            {
                HasRecord = true,
                HasWatched = record.ViewDuration > 0,
                IsCompleted = record.IsCompleted,
                WatchProgress = record.WatchProgress,
                WatchProgressPercent = record.WatchProgressPercent,
                ViewDuration = record.ViewDuration,
                StartTime = record.StartTime,
                LastWatchTime = record.LastWatchTime
            };
        }

        /// <summary>
        /// 开始观看（记录开始时间）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> StartWatchingAsync(string userId, int batchId, CurrentUserInfoDto currentUserInfo)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);
            if (record == null)
                throw new BusinessException("用户批次记录不存在，请先创建记录");

            // 如果还没有开始时间，设置开始时间
            if (record.StartTime == null)
            {
                record.StartTime = DateTime.Now;
                record.UpdateTime = DateTime.Now;

                var user = await _userDAL.GetByIdAsync(userId);
                var batch = await _batchDAL.GetByIdAsync(batchId);

                _logService.Log("UserBatchRecord", $"用户 {user?.Nickname} 开始观看批次 {batch?.Name}",
                    userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

                return await _userBatchRecordDAL.GetDbContext().SaveChangesAsync() > 0;
            }

            return true;
        }

        /// <summary>
        /// 批量更新观看进度（用于定时同步）
        /// </summary>
        /// <param name="progressUpdates">进度更新列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>成功更新的数量</returns>
        public async Task<int> BatchUpdateWatchProgressAsync(List<BatchWatchProgressUpdateDto> progressUpdates, CurrentUserInfoDto currentUserInfo)
        {
            int successCount = 0;

            foreach (var update in progressUpdates)
            {
                try
                {
                    var success = await UpdateWatchProgressAsync(update.UserId, new WatchProgressUpdateDto
                    {
                        BatchId = update.BatchId,
                        ViewDuration = update.ViewDuration,
                        WatchProgress = update.WatchProgress,
                        IsCompleted = update.IsCompleted
                    }, currentUserInfo);

                    if (success) successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量更新观看进度失败，用户ID: {UserId}, 批次ID: {BatchId}",
                        update.UserId, update.BatchId);
                }
            }

            return successCount;
        }

        #endregion

        #region 答题管理

        /// <summary>
        /// 提交答题结果
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="answerDto">答题结果DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SubmitAnswerAsync(string userId, AnswerSubmitDto answerDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证记录是否存在
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, answerDto.BatchId);
            if (record == null)
                throw new BusinessException("用户批次记录不存在");

            // 检查是否已答题
            if (record.TotalQuestions > 0)
                throw new BusinessException("用户已提交过答题结果");

            // 验证答题数据
            if (answerDto.CorrectAnswers > answerDto.TotalQuestions)
                throw new BusinessException("正确答案数不能大于总题目数");

            // 更新答题结果
            var success = await _userBatchRecordDAL.UpdateAnswerResultAsync(
                userId,
                answerDto.BatchId,
                answerDto.TotalQuestions,
                answerDto.CorrectAnswers,
                answerDto.AnswerDetails,
                currentUserInfo);

            if (success)
            {
                // 答题完成时记录日志
                var user = await _userDAL.GetByIdAsync(userId);
                _ = await _batchDAL.GetByIdAsync(answerDto.BatchId);
                var correctRate = Math.Round((decimal)answerDto.CorrectAnswers / answerDto.TotalQuestions * 100, 2);

                _logService.Log("UserBatchRecord", $"用户 {user?.Nickname} 完成答题，正确率 {correctRate}%",
                    userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

                _logger.LogInformation("用户 {UserId} 完成批次 {BatchId} 答题，正确率 {CorrectRate}%",
                    userId, answerDto.BatchId, correctRate);
            }

            return success;
        }

        /// <summary>
        /// 检查用户是否已答题
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已答题</returns>
        public async Task<bool> HasAnsweredAsync(string userId, int batchId)
        {
            return await _userBatchRecordDAL.HasAnsweredAsync(userId, batchId);
        }

        /// <summary>
        /// 获取用户答题状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>答题状态信息</returns>
        public async Task<AnswerStatusDto> GetAnswerStatusAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);

            if (record == null)
            {
                return new AnswerStatusDto
                {
                    HasRecord = false,
                    HasAnswered = false,
                    TotalQuestions = 0,
                    CorrectAnswers = 0,
                    CorrectRate = 0
                };
            }

            return new AnswerStatusDto
            {
                HasRecord = true,
                HasAnswered = record.HasAnswered,
                TotalQuestions = record.TotalQuestions,
                CorrectAnswers = record.CorrectAnswers,
                CorrectRate = record.CorrectRate,
                AnswerTime = record.AnswerTime,
                CanAnswer = !record.HasAnswered && record.IsCompleted // 只有完播后才能答题
            };
        }

        /// <summary>
        /// 获取用户答题详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>答题详情</returns>
        public async Task<AnswerDetailDto?> GetAnswerDetailAsync(string userId, int batchId, UserInfo currentUserInfo)
        {
            // 权限检查：只有管理员或用户本人可以查看答题详情
            if (currentUserInfo.UserType == 3 && currentUserInfo.UserId != userId)
            {
                // 员工只能查看自己绑定用户的答题详情
                var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);
                if (accessibleUserIds != null && !accessibleUserIds.Contains(userId))
                {
                    throw new BusinessException("无权查看该用户的答题详情");
                }
            }

            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);
            if (record == null || !record.HasAnswered)
                return null;

            var user = await _userDAL.GetByIdAsync(userId);
            var batch = await _batchDAL.GetByIdAsync(batchId);

            return new AnswerDetailDto
            {
                UserId = userId,
                UserNickname = user?.Nickname,
                BatchId = batchId,
                BatchName = batch?.Name,
                TotalQuestions = record.TotalQuestions,
                CorrectAnswers = record.CorrectAnswers,
                CorrectRate = record.CorrectRate,
                AnswerDetails = record.AnswerDetails,
                AnswerTime = record.AnswerTime
            };
        }

        /// <summary>
        /// 验证答题资格
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>验证结果</returns>
        public async Task<AnswerEligibilityDto> CheckAnswerEligibilityAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);

            if (record == null)
            {
                return new AnswerEligibilityDto
                {
                    CanAnswer = false,
                    Reason = "用户批次记录不存在"
                };
            }

            if (record.HasAnswered)
            {
                return new AnswerEligibilityDto
                {
                    CanAnswer = false,
                    Reason = "用户已完成答题"
                };
            }

            if (!record.IsCompleted)
            {
                return new AnswerEligibilityDto
                {
                    CanAnswer = false,
                    Reason = "用户尚未完播视频"
                };
            }

            // 检查批次是否在有效期内
            var batch = await _batchDAL.GetByIdAsync(batchId);
            if (batch == null)
            {
                return new AnswerEligibilityDto
                {
                    CanAnswer = false,
                    Reason = "批次不存在"
                };
            }

            if (DateTime.Now > batch.EndTime)
            {
                return new AnswerEligibilityDto
                {
                    CanAnswer = false,
                    Reason = "批次已结束"
                };
            }

            return new AnswerEligibilityDto
            {
                CanAnswer = true,
                Reason = "可以答题"
            };
        }

        #endregion

        #region 红包管理

        /// <summary>
        /// 发放红包
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="rewardDto">红包发放DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> GrantRewardAsync(string userId, RewardGrantDto rewardDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证记录是否存在
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, rewardDto.BatchId);
            if (record == null)
                throw new BusinessException("用户批次记录不存在");

            // 检查是否已发放红包
            if (record.RewardAmount > 0)
                throw new BusinessException("用户已获得红包");

            // 验证是否满足发放条件（可根据业务需求调整）
            if (!record.IsCompleted)
                throw new BusinessException("用户未完播，不能发放红包");

            // 更新红包信息
            var success = await _userBatchRecordDAL.UpdateRewardAsync(
                userId,
                rewardDto.BatchId,
                rewardDto.RewardAmount,
                currentUserInfo,
                rewardDto.TransactionId,
                rewardDto.OutTradeNo);

            if (success)
            {
                // 红包发放成功时记录日志
                var user = await _userDAL.GetByIdAsync(userId);
                _ = await _batchDAL.GetByIdAsync(rewardDto.BatchId);

                _logService.Log("UserBatchRecord", $"向用户 {user?.Nickname} 发放红包 {rewardDto.RewardAmount} 元",
                    userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

                _logger.LogInformation("向用户 {UserId} 发放红包 {Amount} 元，批次 {BatchId}",
                    userId, rewardDto.RewardAmount, rewardDto.BatchId);
            }

            return success;
        }

        /// <summary>
        /// 更新红包状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="statusDto">红包状态更新DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRewardStatusAsync(string userId, RewardStatusUpdateDto statusDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证记录是否存在
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, statusDto.BatchId);
            if (record == null)
                throw new BusinessException("用户批次记录不存在");

            // 更新红包状态
            var success = await _userBatchRecordDAL.UpdateRewardStatusAsync(
                userId,
                statusDto.BatchId,
                statusDto.RewardStatus,
                currentUserInfo,
                statusDto.FailReason,
                statusDto.TransactionId,
                statusDto.OutTradeNo);

            if (success)
            {
                var user = await _userDAL.GetByIdAsync(userId);
                var statusText = statusDto.RewardStatus switch
                {
                    0 => "未发放",
                    1 => "发放成功",
                    2 => "发放失败",
                    _ => "未知状态"
                };

                _logService.Log("UserBatchRecord", $"更新用户 {user?.Nickname} 红包状态为 {statusText}",
                    userId: currentUserInfo.UserId, username: currentUserInfo.UserName);
            }

            return success;
        }

        /// <summary>
        /// 检查用户是否已获得红包
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已获得红包</returns>
        public async Task<bool> HasRewardAsync(string userId, int batchId)
        {
            return await _userBatchRecordDAL.HasRewardAsync(userId, batchId);
        }

        /// <summary>
        /// 获取用户红包状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>红包状态信息</returns>
        public async Task<RewardStatusDto> GetRewardStatusAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);

            if (record == null)
            {
                return new RewardStatusDto
                {
                    HasRecord = false,
                    HasReward = false,
                    RewardAmount = 0,
                    RewardStatus = 0,
                    RewardStatusText = "未发放"
                };
            }

            return new RewardStatusDto
            {
                HasRecord = true,
                HasReward = record.HasReward,
                RewardAmount = record.RewardAmount,
                RewardStatus = record.RewardStatus,
                RewardStatusText = record.RewardStatus switch
                {
                    0 => "未发放",
                    1 => "发放成功",
                    2 => "发放失败",
                    _ => "未知状态"
                },
                RewardTime = record.RewardTime,
                RewardFailReason = record.RewardFailReason,
                RewardRetryCount = record.RewardRetryCount,
                TransactionId = record.TransactionId,
                OutTradeNo = record.OutTradeNo,
                CanReceiveReward = record.IsCompleted && record.HasAnswered && record.RewardAmount == 0
            };
        }

        /// <summary>
        /// 验证红包发放资格
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>验证结果</returns>
        public async Task<RewardEligibilityDto> CheckRewardEligibilityAsync(string userId, int batchId)
        {
            var record = await _userBatchRecordDAL.GetUserBatchRecordAsync(userId, batchId);

            if (record == null)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "用户批次记录不存在"
                };
            }

            if (record.RewardAmount > 0)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "用户已获得红包"
                };
            }

            if (!record.IsCompleted)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "用户尚未完播视频"
                };
            }

            if (!record.HasAnswered)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "用户尚未完成答题"
                };
            }

            // 检查批次红包配置
            var batch = await _batchDAL.GetByIdAsync(batchId);
            if (batch == null)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "批次不存在"
                };
            }

            if (batch.RedPacketAmount <= 0)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "批次未配置红包"
                };
            }

            if (DateTime.Now > batch.EndTime)
            {
                return new RewardEligibilityDto
                {
                    CanReceiveReward = false,
                    Reason = "批次已结束"
                };
            }

            return new RewardEligibilityDto
            {
                CanReceiveReward = true,
                Reason = "可以发放红包",
                SuggestedAmount = batch.RedPacketAmount
            };
        }

        /// <summary>
        /// 批量发放红包
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="rewardAmount">红包金额</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>发放结果</returns>
        public async Task<BatchRewardResultDto> BatchGrantRewardAsync(int batchId, decimal rewardAmount, CurrentUserInfoDto currentUserInfo)
        {
            // 获取符合条件的用户记录
            var eligibleRecords = await GetEligibleRewardRecordsAsync(batchId);

            var result = new BatchRewardResultDto
            {
                BatchId = batchId,
                TotalEligible = eligibleRecords.Count,
                SuccessCount = 0,
                FailedCount = 0,
                FailedUsers = []
            };

            foreach (var record in eligibleRecords)
            {
                try
                {
                    var success = await _userBatchRecordDAL.UpdateRewardAsync(
                        record.UserId,
                        batchId,
                        rewardAmount,
                        currentUserInfo);

                    if (success)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailedCount++;
                        var user = await _userDAL.GetByIdAsync((string)record.UserId);
                        result.FailedUsers.Add(user?.Nickname ?? $"用户{record.UserId}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    var user = await _userDAL.GetByIdAsync((string)record.UserId);
                    result.FailedUsers.Add(user?.Nickname ?? $"用户{record.UserId}");

                    _logger.LogError(ex, "批量发放红包失败，用户ID: {UserId}, 批次ID: {BatchId}",
                        record.UserId, batchId);
                }
            }

            // 记录日志
            var batch = await _batchDAL.GetByIdAsync(batchId);
            _logService.Log("UserBatchRecord", $"批量发放红包完成，批次: {batch?.Name}，成功: {result.SuccessCount}，失败: {result.FailedCount}",
                userId: currentUserInfo.UserId, username: currentUserInfo.UserName);

            return result;
        }

        /// <summary>
        /// 获取符合红包发放条件的记录
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>符合条件的记录列表</returns>
        private async Task<List<UserBatchRecord>> GetEligibleRewardRecordsAsync(int batchId)
        {
            return await _userBatchRecordDAL.GetQueryable(new UserBatchRecordDAL.Queryable())
                .Where(ubr => ubr.BatchId == batchId
                    && ubr.IsCompleted
                    && ubr.TotalQuestions > 0
                    && ubr.RewardAmount == 0)
                .ToListAsync();
        }

        #endregion

        #region 统计和查询

        /// <summary>
        /// 根据日期范围获取记录（支持权限控制）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>记录列表</returns>
        public async Task<List<UserBatchRecordSummaryDto>> GetRecordsByDateRangeAsync(DateTime startDate, DateTime endDate, CurrentUserInfoDto currentUserInfo)
        {
            // 转换为UserInfo类型以使用权限控制方法
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };

            // 根据用户权限获取可访问的用户ID列表
            var accessibleUserIds = await GetAccessibleUserIdsAsync(userInfo);

            var query = _userBatchRecordDAL.GetQueryable(new UserBatchRecordDAL.Queryable())
                .Where(r => r.CreateTime >= startDate && r.CreateTime <= endDate);

            // 应用权限过滤
            if (accessibleUserIds != null)
            {
                query = query.Where(r => accessibleUserIds.Contains(r.UserId));
            }

            var records = await query.ToListAsync();
            var result = new List<UserBatchRecordSummaryDto>();

            foreach (var record in records)
            {
                result.Add(new UserBatchRecordSummaryDto
                {
                    Id = record.Id,
                    UserId = record.UserId,
                    BatchId = record.BatchId,
                    IsCompleted = record.IsCompleted,
                    TotalQuestions = record.TotalQuestions,
                    CorrectAnswers = record.CorrectAnswers,
                    CorrectRate = record.TotalQuestions > 0 ? (decimal)record.CorrectAnswers / record.TotalQuestions * 100 : 0,
                    RewardAmount = record.RewardAmount,
                    CreateTime = record.CreateTime
                });
            }

            return result;
        }

        /// <summary>
        /// 获取批次统计数据（支持权限控制）
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>批次统计数据</returns>
        public async Task<BatchStatisticsFromRecordDto> GetBatchStatisticsAsync(int batchId, UserInfo currentUserInfo)
        {
            // 根据用户权限获取可访问的用户ID列表
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            // 获取统计数据
            var statistics = await _userBatchRecordDAL.GetBatchStatisticsAsync(batchId, accessibleUserIds);

            // 获取批次信息
            var batch = await _batchDAL.GetByIdAsync(batchId);

            // 转换为DTO
            return new BatchStatisticsFromRecordDto
            {
                BatchId = batchId,
                BatchName = batch?.Name,
                TotalParticipants = statistics.TotalParticipants, // 修正：现在是总用户数（所有可访问用户）
                ViewerCount = statistics.ViewerCount, // 实际观看用户数
                UnwatchedUserCount = statistics.UnwatchedUserCount, // 未观看用户数 = 总用户数 - 观看用户数
                CompletedViewerCount = statistics.CompletedViewerCount,
                // 完播率：基于观看用户数计算
                CompleteRate = statistics.ViewerCount > 0 ?
                    Math.Round((decimal)statistics.CompletedViewerCount / statistics.ViewerCount * 100, 2) : 0,
                AnswerCount = statistics.AnswerCount,
                // 答题率：基于总用户数计算
                AnswerRate = statistics.TotalParticipants > 0 ?
                    Math.Round((decimal)statistics.AnswerCount / statistics.TotalParticipants * 100, 2) : 0,
                AverageCorrectRate = statistics.AverageCorrectRate,
                RewardCount = statistics.RewardCount,
                TotalRewardAmount = statistics.TotalRewardAmount,
                SuccessRewardCount = statistics.SuccessRewardCount,
                RewardSuccessRate = statistics.RewardCount > 0 ?
                    Math.Round((decimal)statistics.SuccessRewardCount / statistics.RewardCount * 100, 2) : 0,
                AverageViewDuration = statistics.AverageViewDuration,
                AverageWatchProgress = statistics.AverageWatchProgress
            };
        }

        /// <summary>
        /// 获取批次记录列表（支持权限控制）
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>批次记录列表</returns>
        public async Task<List<UserBatchRecordResponseDto>> GetBatchRecordsAsync(int batchId, UserInfo currentUserInfo)
        {
            // 根据用户权限获取可访问的用户ID列表
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            // 获取所有用户的记录列表（包括没有观看记录的用户）
            var recordsWithAllUsers = await _userBatchRecordDAL.GetBatchRecordsWithAllUsersAsync(batchId, accessibleUserIds);

            // 转换为响应DTO
            var result = new List<UserBatchRecordResponseDto>();

            // 获取所有可访问的用户ID
            List<string> allUserIds;
            if (accessibleUserIds == null)
            {
                // 超管：获取所有用户ID
                allUserIds = await _userDAL.GetQueryable()
                    .Select(u => u.Id)
                    .ToListAsync();
            }
            else
            {
                allUserIds = accessibleUserIds;
            }

            for (int i = 0; i < recordsWithAllUsers.Count; i++)
            {
                var record = recordsWithAllUsers[i];
                var userId = allUserIds[i];

                if (record != null)
                {
                    // 有记录的用户，正常转换
                    result.Add(await ConvertToResponseDtoAsync(record));
                }
                else
                {
                    // 没有记录的用户，创建默认记录
                    result.Add(await CreateDefaultUserBatchRecordResponseDto(userId, batchId));
                }
            }

            return result;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 为没有观看记录的用户创建默认响应DTO
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>默认的用户批次记录响应DTO</returns>
        private async Task<UserBatchRecordResponseDto> CreateDefaultUserBatchRecordResponseDto(string userId, int batchId)
        {
            // 获取用户信息
            var user = await _userDAL.GetByIdAsync(userId);
            var batch = await _batchDAL.GetByIdAsync(batchId);
            _ = batch != null ? await _videoDAL.GetByIdAsync(batch.VideoId) : null;

            // 获取员工信息
            string? employeeName = null;
            if (!string.IsNullOrEmpty(user?.EmployeeId))
            {
                var employee = await _sysUserDAL.GetByIdAsync(user.EmployeeId);
                employeeName = employee?.RealName;
            }

            return new UserBatchRecordResponseDto
            {
                Id = 0, // 没有记录，ID为0
                UserId = userId,
                UserNickname = user?.Nickname,
                UserAvatar = user?.Avatar,
                EmployeeId = user?.EmployeeId,
                EmployeeName = employeeName,
                BatchId = batchId,
                BatchName = batch?.Name,
                ViewDuration = 0, // 默认观看时长为0
                WatchProgress = 0, // 默认观看进度为0
                IsCompleted = false, // 默认未完播
                StartTime = null, // 没有开始时间
                LastWatchTime = null, // 没有最后观看时间
                TotalQuestions = 0, // 默认题目数为0
                CorrectAnswers = 0, // 默认正确答案数为0
                CorrectRate = 0, // 默认正确率为0
                HasAnswered = false, // 默认未答题
                AnswerTime = null, // 没有答题时间
                RewardAmount = 0, // 默认红包金额为0
                RewardStatus = 0, // 默认红包状态为未发放
                RewardTime = null, // 没有红包发放时间
                PromotionLink = null, // 没有推广链接
                CreateTime = DateTime.Now, // 使用当前时间作为创建时间
                UpdateTime = DateTime.Now // 使用当前时间作为更新时间
            };
        }

        /// <summary>
        /// 转换为响应DTO
        /// </summary>
        /// <param name="record">用户批次记录</param>
        /// <returns>响应DTO</returns>
        private async Task<UserBatchRecordResponseDto> ConvertToResponseDtoAsync(UserBatchRecord record)
        {
            // 获取关联数据
            var user = await _userDAL.GetByIdAsync(record.UserId);
            var batch = await _batchDAL.GetByIdAsync(record.BatchId);
            var video = batch != null ? await _videoDAL.GetByIdAsync(batch.VideoId) : null;

            // 获取员工信息
            string? employeeName = null;
            if (!string.IsNullOrEmpty(user?.EmployeeId))
            {
                // 这里需要根据实际的员工查询方法调整
                // var employee = await _sysUserDAL.GetByIdAsync(user.EmployeeId);
                // employeeName = employee?.UserName;
            }

            return new UserBatchRecordResponseDto
            {
                Id = record.Id,
                UserId = record.UserId,
                UserNickname = user?.Nickname,
                UserAvatar = user?.Avatar,
                EmployeeId = user?.EmployeeId,
                EmployeeName = employeeName,
                BatchId = record.BatchId,
                BatchName = batch?.Name,
                VideoTitle = video?.Title,

                // 观看相关
                ViewDuration = record.ViewDuration,
                WatchProgress = record.WatchProgress,
                WatchProgressPercent = record.WatchProgressPercent,
                IsCompleted = record.IsCompleted,
                StartTime = record.StartTime,
                LastWatchTime = record.LastWatchTime,

                // 答题相关
                TotalQuestions = record.TotalQuestions,
                CorrectAnswers = record.CorrectAnswers,
                CorrectRate = record.CorrectRate,
                HasAnswered = record.HasAnswered,
                AnswerTime = record.AnswerTime,

                // 红包相关
                RewardAmount = record.RewardAmount,
                RewardStatus = record.RewardStatus,
                RewardStatusText = record.RewardStatus switch
                {
                    0 => "未发放",
                    1 => "发放成功",
                    2 => "发放失败",
                    _ => "未知状态"
                },
                HasReward = record.HasReward,
                RewardTime = record.RewardTime,
                RewardFailReason = record.RewardFailReason,

                PromotionLink = record.PromotionLink,
                CreateTime = record.CreateTime,
                UpdateTime = record.UpdateTime
            };
        }

        /// <summary>
        /// 如果需要，创建IP用户
        /// 判断用户ID是否为负数（IP用户标识），如果是则创建IP用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>创建的用户或null</returns>
        private async Task<User?> CreateIPUserIfNeededAsync(string userId)
        {
            try
            {
                // 检查是否为IP用户ID（以"ip_"开头或MD5格式）
                if (userId.StartsWith("ip_") || (userId.Length == 32 && userId.All(c => char.IsLetterOrDigit(c))))
                {
                    // 创建IP用户
                    var ipUser = new User
                    {
                        // 注意：User实体的Id是int类型，这里需要生成一个唯一的int ID
                        // 可以使用哈希码或其他方法生成
                        OpenId = null, // IP用户没有OpenId
                        UnionId = null, // IP用户没有UnionId
                        Nickname = $"访客_{userId.Substring(0, Math.Min(8, userId.Length))}",
                        Avatar = null,
                        EmployeeId = null, // IP用户不绑定员工
                        CreateTime = DateTime.Now,
                        LastLogin = DateTime.Now,
                        CreatedBy = "SYSTEM", // 系统创建
                        CreatorName = "IP用户系统"
                    };

                    // 保存到数据库（使用普通方法）
                    await _userDAL.AddAsync(ipUser);

                    // 记录日志
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "用户管理",
                        Operation = "IP用户自动创建",
                        BusinessObject = "User",
                        ObjectId = userId,
                        DetailedInfo = $"自动创建IP用户，ID：{userId}，昵称：{ipUser.Nickname}",
                        AfterData = ipUser,
                        Level = LogLevel.Information
                    });

                    return ipUser;
                }

                return null; // 不是IP用户ID
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，让上层处理
                await _logService.LogBusinessOperationAsync(new BusinessLogDto
                {
                    Module = "用户管理",
                    Operation = "IP用户创建失败",
                    BusinessObject = "User",
                    ObjectId = userId,
                    DetailedInfo = $"创建IP用户失败：{ex.Message}",
                    Level = LogLevel.Error
                });

                return null;
            }
        }

        #endregion
    }
}
